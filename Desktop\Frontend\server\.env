# Server Configuration
PORT=3001
NODE_ENV=development

# Google Cloud Configuration (for local development)
GOOGLE_CLOUD_PROJECT_ID=lexiplain-local-dev
GOOGLE_APPLICATION_CREDENTIALS=./keys/service-account-key.json

# Google Cloud Services
GCP_REGION=us-central1
VERTEX_AI_LOCATION=us-central1
FIRESTORE_DATABASE=(default)
STORAGE_BUCKET_NAME=your-bucket-name

# Google Vision API
GOOGLE_VISION_API_KEY=your-vision-api-key

# Google Speech Services
GOOGLE_SPEECH_API_KEY=your-speech-api-key

# OpenAI (Backup LLM)
OPENAI_API_KEY=your-openai-api-key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Limits
MAX_FILE_SIZE_MB=50
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,jpg,jpeg,png

# Security
CORS_ORIGIN=http://localhost:8080
API_SECRET_KEY=your-secret-key-here

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Voice Interface
VOICE_LANGUAGE_CODE=en-US
TTS_VOICE_NAME=en-US-Neural2-D

# AI Model Configuration
LEGAL_MODEL_NAME=gemini-1.5-pro
EMBEDDING_MODEL=textembedding-gecko
MAX_TOKENS=4096
TEMPERATURE=0.3

# RAG Configuration
VECTOR_DIMENSIONS=768
SIMILARITY_THRESHOLD=0.7
MAX_CONTEXT_LENGTH=8000

# Demo Mode
ENABLE_DEMO_MODE=true
DEMO_DOCUMENTS_PATH=./demo-documents
