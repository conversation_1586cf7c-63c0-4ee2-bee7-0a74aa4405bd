{"name": "fusion-starter", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest --run", "format.fix": "prettier --write .", "typecheck": "tsc"}, "dependencies": {"socket.io-client": "^4.7.4"}, "devDependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@swc/core": "^1.13.3", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.84.2", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/three": "^0.176.0", "@vitejs/plugin-react-swc": "^4.0.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.12", "globals": "^16.3.0", "input-otp": "^1.4.2", "lucide-react": "^0.539.0", "next-themes": "^0.4.6", "postcss": "^8.5.6", "prettier": "^3.6.2", "react": "^18.3.1", "react-day-picker": "^9.8.1", "react-dom": "^18.3.1", "react-hook-form": "^7.62.0", "react-resizable-panels": "^3.0.4", "react-router-dom": "^6.30.1", "recharts": "^2.12.7", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "three": "^0.176.0", "typescript": "^5.9.2", "vaul": "^1.1.2", "vite": "^7.1.2", "vitest": "^3.2.4"}, "packageManager": "pnpm@10.14.0+sha512.ad27a79641b49c3e481a16a805baa71817a04bbe06a38d17e60e2eaee83f6a146c6a688125f5792e48dd5ba30e7da52a5cda4c3992b9ccf333f9ce223af84748"}