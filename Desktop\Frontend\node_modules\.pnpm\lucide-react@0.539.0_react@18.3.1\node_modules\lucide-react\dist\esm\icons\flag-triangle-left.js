/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    { d: "M18 22V2.8a.8.8 0 0 0-1.17-.71L5.45 7.78a.8.8 0 0 0 0 1.44L18 15.5", key: "rbbtmw" }
  ]
];
const FlagTriangleLeft = createLucideIcon("flag-triangle-left", __iconNode);

export { __iconNode, FlagTriangleLeft as default };
//# sourceMappingURL=flag-triangle-left.js.map
