/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 6v6", key: "1ipuwl" }],
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }]
];
const Clock12 = createLucideIcon("clock-12", __iconNode);

export { __iconNode, Clock12 as default };
//# sourceMappingURL=clock-12.js.map
