/** @import must precede all other statements */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;600;700;800&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /**
   * Tailwind CSS theme
   * tailwind.config.ts expects the following color variables to be expressed as HSL values.
   * A different format will require also updating the theme in tailwind.config.ts.
  */
  :root {
    /* Premium light palette in HSL (used by tailwind.config.ts) */
    --background: 0 0% 100%;
    --foreground: 224 71% 4%;

    --card: 0 0% 100%;
    --card-foreground: 224 71% 4%;

    --popover: 0 0% 100%;
    --popover-foreground: 224 71% 4%;

    --primary: 210 90% 56%; /* soft blue */
    --primary-foreground: 0 0% 100%;

    --secondary: 210 40% 97%;
    --secondary-foreground: 224 71% 4%;

    --muted: 210 40% 96%;
    --muted-foreground: 220 14% 46%;

    --accent: 210 100% 98%;
    --accent-foreground: 224 71% 4%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 210 24% 92%;
    --input: 210 24% 92%;
    --ring: 210 90% 56%;

    --radius: 1rem;

    /* Sidebar tokens (kept for components that rely on them) */
    --sidebar-background: 0 0% 99%;
    --sidebar-foreground: 224 13% 20%;
    --sidebar-primary: 210 90% 56%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 40% 97%;
    --sidebar-accent-foreground: 224 13% 20%;
    --sidebar-border: 210 24% 92%;
    --sidebar-ring: 210 90% 56%;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 0 0% 100%;

    --card: 224 71% 6%;
    --card-foreground: 0 0% 100%;

    --popover: 224 71% 6%;
    --popover-foreground: 0 0% 100%;

    --primary: 210 90% 60%;
    --primary-foreground: 0 0% 100%;

    --secondary: 224 20% 12%;
    --secondary-foreground: 0 0% 100%;

    --muted: 224 20% 12%;
    --muted-foreground: 217 19% 72%;

    --accent: 224 20% 12%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62% 30%;
    --destructive-foreground: 0 0% 100%;

    --border: 224 20% 18%;
    --input: 224 20% 18%;
    --ring: 210 90% 60%;

    --sidebar-background: 224 20% 12%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 210 90% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 224 20% 18%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 224 20% 18%;
    --sidebar-ring: 210 90% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html { scroll-behavior: smooth; }

  body {
    @apply bg-background text-foreground antialiased transition-colors duration-300;
    font-family: "Plus Jakarta Sans", Inter, ui-sans-serif, system-ui,
      -apple-system, Segoe UI, Roboto, "Helvetica Neue", Arial, "Noto Sans",
      "Apple Color Emoji", "Segoe UI Emoji";
  }
}

/* Glassmorphism utilities */
@layer utilities {
  .glass {
    @apply bg-white/10 dark:bg-white/10 backdrop-blur-xl border border-white/30 dark:border-white/15 shadow-[0_8px_30px_rgba(0,0,0,0.06)];
  }
  .glass-soft {
    @apply bg-white/60 dark:bg-white/15 backdrop-blur-2xl border border-white/30 dark:border-white/15 shadow-[0_8px_30px_rgba(0,0,0,0.08)];
  }
  .surface {
    @apply bg-white/70 dark:bg-white/10 backdrop-blur-xl border border-white/40 dark:border-white/10;
  }
  .elevated {
    @apply shadow-[0_10px_40px_rgba(2,6,23,0.06)];
  }
  .brand-gradient {
    background-image: radial-gradient(60% 60% at 50% 0%, rgba(56,189,248,0.12) 0%, rgba(59,130,246,0.10) 35%, rgba(99,102,241,0.06) 60%, transparent 100%),
      linear-gradient(180deg, rgba(255,255,255,0.9), rgba(255,255,255,0.85));
  }
  .bg-grid {
    background-image: linear-gradient(90deg, rgba(0,0,0,0.04) 1px, transparent 1px), linear-gradient(rgba(0,0,0,0.04) 1px, transparent 1px);
    background-size: 24px 24px;
    mask-image: radial-gradient(ellipse 60% 60% at 50% 20%, black 60%, transparent 100%);
  }
  .dark .bg-grid {
    background-image: linear-gradient(90deg, rgba(255,255,255,0.06) 1px, transparent 1px), linear-gradient(rgba(255,255,255,0.06) 1px, transparent 1px);
  }
  .shine { position: relative; overflow: hidden; will-change: transform; }
  .shine::before {
    content: ""; position: absolute; inset: 0; transform: translateX(-120%);
    background: linear-gradient(110deg, transparent 30%, rgba(255,255,255,0.7) 45%, transparent 60%);
    animation: shimmer 2.4s infinite;
  }
  .reveal-hidden { opacity: 0; transform: translateY(12px); }
  .reveal-show { opacity: 1; transform: translateY(0); transition: all 700ms cubic-bezier(0.22, 1, 0.36, 1); }
}

@keyframes shimmer {
  100% { transform: translateX(120%); }
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
}
