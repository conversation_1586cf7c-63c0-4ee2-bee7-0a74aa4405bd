/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 12h.01", key: "1kxr2c" }],
  ["path", { d: "M18 9V6a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v14", key: "1bnhmg" }],
  ["path", { d: "M2 20h8", key: "10ntw1" }],
  ["path", { d: "M20 17v-2a2 2 0 1 0-4 0v2", key: "pwaxnr" }],
  ["rect", { x: "14", y: "17", width: "8", height: "5", rx: "1", key: "15pjcy" }]
];
const DoorClosedLocked = createLucideIcon("door-closed-locked", __iconNode);

export { __iconNode, DoorClosedLocked as default };
//# sourceMappingURL=door-closed-locked.js.map
